import { createRouter, createWebHistory } from 'vue-router';
import Optimization from './components/Optimization.vue';
import ProductivityChartsDashboard from './components/ProductivityChartsDashboard.vue';
import EvaluationDashboard from './components/EvaluationDashboard.vue';
import BusinessDashboard from './components/BusinessDashboard.vue';
import LandingPage from './views/LandingPage.vue';
import SpaceHealthDashboard from './components/SpaceHealthDashboard.vue';
import StoreCluster from './components/StoreCluster.vue';
import ConceptDashboard from './components/ConceptDashboard.vue';
import AuthCallback from './components/AuthCallback.vue';
import UserDashboard from './components/UserDashboard.vue'; // Add this import
import OutlierHandle from './components/OutlierHandle.vue'; // Add this import
import SpaceDataSummary from './components/SpaceDataSummary.vue'; // Add this import
import ControlStoreSelection from './components/ControlStoreSelection.vue';
import HomePage from './components/homePage.vue'; // Add this import
import BuildOptimiser from './components/buildOptimiserPage.vue';
import PerformanceCalculation from './components/PerformanceCalculation.vue'
import { Component } from 'lucide-vue-next';
import Sidebar from './components/Sidebar.vue'
import Layout from './Layout.vue'

const routes = [
  { path: '/', name: 'landing', component: LandingPage },
  { path: '/callback', component: AuthCallback },
  {
    path: '/spaceoptimization',
    component:Layout,
    children:[
      {
        name: 'UserDashboard',
        component: UserDashboard,
        path:'//user-dashboard',
        props: true,
        meta: { requiresSuperAdmin: true }
      },
    ]
    },
  {
    path: '/spaceoptimization',
    component:Layout,
    children:[
      {
        name: 'HomePage',
        component: HomePage,
        path:'/HomePage/',
        props: true,

      },
      {
        path: '/BuildOptimiser',
        name: 'BuildOptimiser',
        component: BuildOptimiser,
        props: true
      }
      

    ]
  },
  {path:'/saturation',component:ProductivityChartsDashboard}
];

const webHistory = createWebHistory(import.meta.env.VITE_BASE_PATH);
const router = createRouter({
  history: webHistory,
  routes,
});

router.beforeEach((to, from, next) => {
  if (to.meta.requiresSuperAdmin) {
    // Example: get user role from localStorage or a store
    const userRole = localStorage.getItem('role');
    if (userRole === 'superadmin') {
      next();
    } else {
      next({ path: '/' });
    }
  } else {
    next();
  }
});

export default router;