<script setup lang="ts">
import { ref,onMounted,computed } from 'vue';
import Multiselect from 'vue-multiselect'
import { useStepsStore } from '../stores/NavigationStore.js'
import FilterBarTest from './FilterBarTest.vue';


// FilterBar logic
const groupOptions = ref(['Fashion', 'Home', 'Electronics']);
const departmentOptions = ref(['Bags', 'Fashion Accessories', 'Hair Accessories', 'Bathroom', 'Floor Covering', 'Home Decor', 'Soft Furnishing', 'Tabletop']);
const classOptions = ref(['Class A', 'Class B', 'Class C']);
const subClassOptions = ref(['Subclass 1', 'Subclass 2', 'Subclass 3']);
const storeNameOptions = ref(['Dalma Mall', 'Dalma Mall', 'Dalma Mall']);
const storeIdOptions = ref(['21404', '21404', '21404']);
const storeOptions = ref(['Store 1', 'Store 2', 'Store 3']);

const selectedGroup = ref('');
const selectedDepartment = ref('');
const selectedClass = ref('');
const selectedSubClass = ref('');
const selectedStoreName = ref('');
const selectedStoreId = ref('');
const startDate = ref('');
const endDate = ref('');


const preSeasonEnabled = computed(() => {
  return stepsStore.visibleSteps.find(n => n.name === 'Optimization Summary') || null;
});


const stepsStore = useStepsStore()
const dropdowns = [
    { label: 'Group', model: selectedGroup, options: groupOptions.value },
    { label: 'Department', model: selectedDepartment, options: departmentOptions.value },
    { label: 'Class', model: selectedClass, options: classOptions.value },
    { label: 'Sub Class', model: selectedSubClass, options: subClassOptions.value },
    { label: 'Store Name', model: selectedStoreName, options: storeNameOptions.value },
    { label: 'Store ID', model: selectedStoreId, options: storeIdOptions.value },
];

const applyFilters = () => {
    console.log('Applying filters...');
};

const filtersList = [
    { key: 'storeid', label: 'Store ID', options: [{ label: 'Storeid 1', value: 'storeid1' }, { label: 'Storeid 2', value: 'storeid2' }] },
    { key: 'store', label: 'Store', options: [{ label: 'Store 1', value: 'store1' }, { label: 'Store 2', value: 'store2' }] },
    { key: 'group', label: 'Group', options: [{ label: 'Group A', value: 'groupA' }, { label: 'Group B', value: 'groupB' }] },
    { key: 'department', label: 'Department', options: [{ label: 'Dept X', value: 'deptX' }, { label: 'Dept Y', value: 'deptY' }] },
    { key: 'class', label: 'Class', options: [{ label: 'Class 1', value: 'class1' }, { label: 'Class 2', value: 'class2' }] },
    { key: 'subclass', label: 'Sub Class', options: [{ label: 'Sub 1', value: 'sub1' }, { label: 'Sub 2', value: 'sub2' }] }
]
// SpaceHealthTables logic
const tableData = ref([
    {
        class: 'BATH TEXTILES',
        subClass: 'BATHMATS',
        currLM: 23.7,
        minLM: 28,
        maxLM: 22,
        optimizedLM: 0.2,
        spaceChange: 37.4,
        spaceChangePercent: '20%',
        gmv: 12637,
        optimizedGMV: 23879,
        gmvChangePercent: '12%',
        subClassLevel: 'Increase',
        changeOptions: 12,
        Depth: 9,
        ChangeQty: 110,
    },
    {
        class: 'BATH TEXTILES',
        subClass: 'BATH TOWELS',
        currLM: 37.2,
        minLM: 30,
        maxLM: 28,
        optimizedLM: 0.3,
        spaceChange: 39.4,
        spaceChangePercent: '10%',
        gmv: 12937,
        optimizedGMV: 23979,
        gmvChangePercent: '19%',
        subClassLevel: 'Decrease',
        changeOptions: 15,
        Depth: 6,
        ChangeQty: 120,
    },
    {
        class: 'BATH TEXTILES',
        subClass: 'BATH ROBES',
        currLM: 37.2,
        minLM: 30,
        maxLM: 28,
        optimizedLM: 0.3,
        spaceChange: 39.4,
        spaceChangePercent: '10%',
        gmv: 12937,
        optimizedGMV: 23979,
        gmvChangePercent: '19%',
        subClassLevel: 'Decrease',
        changeOptions: 15,
        Depth: 6,
        ChangeQty: 120,
    },
    {
        class: 'BATH TEXTILES',
        subClass: 'BATH ROBES',
        currLM: 37.2,
        minLM: 30,
        maxLM: 28,
        optimizedLM: 0.3,
        spaceChange: 39.4,
        spaceChangePercent: '10%',
        gmv: 12937,
        optimizedGMV: 23979,
        gmvChangePercent: '19%',
        subClassLevel: 'Decrease',
        changeOptions: 15,
        Depth: 6,
        ChangeQty: 120,
    },
    {
        class: 'BATH TEXTILES',
        subClass: 'BATH ROBES',
        currLM: 37.2,
        minLM: 30,
        maxLM: 28,
        optimizedLM: 0.3,
        spaceChange: 39.4,
        spaceChangePercent: '10%',
        gmv: 12937,
        optimizedGMV: 23979,
        gmvChangePercent: '19%',
        subClassLevel: 'Decrease',
        changeOptions: 15,
        Depth: 6,
        ChangeQty: 120,
    },
    {
        class: 'BATH TEXTILES',
        subClass: 'BATH ROBES',
        currLM: 37.2,
        minLM: 30,
        maxLM: 28,
        optimizedLM: 0.3,
        spaceChange: 39.4,
        spaceChangePercent: '10%',
        gmv: 12937,
        optimizedGMV: 23979,
        gmvChangePercent: '19%',
        subClassLevel: 'Decrease',
        changeOptions: 15,
        Depth: 6,
        ChangeQty: 120,
    },
]);
const isEditingSpaceChange = ref(true);
const isEditingSpaceChangePercent = ref(true);
const selectedFilters = ref({
    store: [],
    group: [],
    department: [],
    class: [],
    subclass: []
})

const runAvailableRange = () => {
    console.log('Running on available range with filters:', selectedFilters.value)
}

const runOptimizer = () => {
    console.log('Running optimizer with filters:', selectedFilters.value)
}   
</script>

<template>
    <div class="flex w-full">
        <div class="flex-1 flex flex-col overflow-hidden">
              
                    <div>
                   <FilterBarTest/>
                        </div>

            <main class="flex-1 p-4 sm:p-6 lg:p-8 ">

                <div class="flex gap-4 w-full justify-end mb-4">
                            
                            <div class="flex gap-2 items-center">
                                <button
                                v-if="preSeasonEnabled && preSeasonEnabled.hidden===false"
                                    class="flex bg-tertiary hover:bg-green-900 text-white  py-1 px-4 rounded"
                                    @click="runAvailableRange">
                                    Run on the available range
                                </button>
                                <button
                                    class="flex bg-tertiary hover:bg-green-900 text-white py-1 px-4 rounded"
                                    @click="runOptimizer">
                                    Run Optimizer
                                </button>
                            </div>
                            
                        </div>
                <div class="rounded-lg shadow p-4">
                    <!-- Horizontal scroll wrapper -->
                    <div class="overflow-x-auto">
                        <!-- Minimum width triggers horizontal scroll -->
                        <div class="overflow-y-auto relative">
                            <table class="w-full text-sm text-left border border-[#60A5FA] bg-white">
                                <thead class="bg-primary">
                                    <tr>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">CLSS_NM</th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">SUB_CLSS_NM</th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">Current LM</th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">Min LM (Derived)
                                        </th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">Max LM (Saturation)
                                        </th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">Optimized LM</th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">Space Change</th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">Space Change(%)</th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">GMV</th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">Optimized GMV</th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">GMV Change(%)</th>
                                        <th class="px-3 py-2 border sticky top-0 bg-primary z-20">Subclass Level
                                            Recommendation</th>
                                        <th v-if="preSeasonEnabled && preSeasonEnabled.hidden===false" class="px-3 py-2 border sticky top-0 bg-primary z-20">Change in Options
                                        </th>
                                        <th v-if="preSeasonEnabled && preSeasonEnabled.hidden===false" class="px-3 py-2 border sticky top-0 bg-primary z-20">Depth</th>
                                        <th v-if="preSeasonEnabled && preSeasonEnabled.hidden===false" class="px-3 py-2 border sticky top-0 bg-primary z-20">Change in Qty</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(row, index) in tableData" :key="index">
                                        <td class="px-3 py-2 border">{{ row.class }}</td>
                                        <td class="px-3 py-2 border">{{ row.subClass }}</td>
                                        <td class="px-3 py-2 border text-center">{{ row.currLM }}</td>
                                        <td class="px-3 py-2 border text-center">{{ row.minLM }}</td>
                                        <td class="px-3 py-2 border text-center">{{ row.maxLM }}</td>
                                        <td class="px-3 py-2 border text-center">{{ row.optimizedLM }}</td>
                                        <!-- <td class="px-3 py-2 border text-center">{{ row.optionPerLm }}</td> -->
                                        <td class="px-3 py-2 border text-center">
                                            <template v-if="isEditingSpaceChange">
                                                <input type="number" v-model="tableData[index].spaceChange"
                                                    class="w-full border rounded px-1" />
                                            </template>
                                            <template v-else>
                                                {{ row.spaceChange }}
                                            </template>
                                        </td>
                                        <!-- <td class="px-3 py-2 border text-center">{{ row.sohPerLm }}</td> -->
                                        <td class="px-3 py-2 border text-center">
                                            <template v-if="isEditingSpaceChangePercent">
                                                <input type="text" v-model="tableData[index].spaceChangePercent"
                                                    class="w-full border rounded px-1" />
                                            </template>
                                            <template v-else>
                                                {{ row.spaceChangePercent }}
                                            </template>
                                        </td>
                                        <td class="px-3 py-2 border text-right">{{ row.gmv }}</td>
                                        <td class="px-3 py-2 border text-right">{{ row.optimizedGMV }}</td>
                                        <td class="px-3 py-2 border text-center">{{ row.gmvChangePercent }}</td>
                                        <td class="px-3 py-2 border text-center">{{ row.subClassLevel }}</td>
                                        <td v-if="preSeasonEnabled && preSeasonEnabled.hidden===false" class="px-3 py-2 border text-center">{{ row.changeOptions }}</td>
                                        <td v-if="preSeasonEnabled && preSeasonEnabled.hidden===false" class="px-3 py-2 border text-center">{{ row.Depth }}</td>
                                        <td v-if="preSeasonEnabled && preSeasonEnabled.hidden===false" class="px-3 py-2 border text-center">{{ row.ChangeQty }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <span class="flex justify-end items-center gap-2 mb-4 mt-4">
                    Store GMV Change
                    <p class="border border-green-600 bg-green-100 text-green-800 px-6 py-2 rounded font-semibold m-0">
                        3%
                    </p>
                </span>
            </main>
        </div>
    </div>
</template>


<style scoped>
.run-button {
    padding: 10px 16px;
    background-color: #3B82F6;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}
</style>