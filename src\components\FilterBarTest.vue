<script setup lang="ts">
import { ref } from 'vue'
import DynamicFilter from './common/DynamicFilter.vue'

const groupOptions = ref(['Fashion', 'Home', 'Electronics'])
const departmentOptions = ref(['Bags', 'Fashion Accessories', 'Hair Accessories', 'Bathroom', 'Floor Covering', 'Home Decor', 'Soft Furnishing', 'Tabletop'])
const classOptions = ref(['Class A', 'Class B', 'Class C'])
const subClassOptions = ref(['Subclass 1', 'Subclass 2', 'Subclass 3'])

const storeNameOptions = ref(['Dalma Mall1', 'Dalma Mall2', 'Dalma Mall3']);
const storeIdOptions = ref(['214034', '211404', '241404']);

const selectedStoreIds = ref<string[]>([])
const selectedStoreNames = ref<string[]>([])
const selectedGroup = ref<string[]>([])
const selectedDepartment = ref<string | null>(null)
const selectedClass = ref<string | null>(null)
const selectedSubClass = ref<string[]>([])

const clearAllFilters = () => {
  selectedGroup.value = []
  selectedDepartment.value = null
  selectedClass.value = null
  selectedSubClass.value = []
}
</script>

<template>
  <div class="p-6 max-w-6xl mx-auto space-y-8">
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
      <div class="space-y-1">
        <label class="block text-xs font-medium text-gray-600">Store ID</label>
        <DynamicFilter
          v-model="selectedStoreIds"
          :multiselect="true"
          label="Groups"
          placeholder="Select Ids"
          :options="storeIdOptions"
          variant="outline"
          size="sm"
        />
      </div>
      <div class="space-y-1">
        <label class="block text-xs font-medium text-gray-600">Store Name</label>
        <DynamicFilter
          v-model="selectedStoreNames"
          :multiselect="true"
          label="Store Names"
          placeholder="Select Stores"
          :options="storeNameOptions"
          variant="outline"
          size="sm"
        />
      </div>
      <div class="space-y-1">
        <label class="block text-xs font-medium text-gray-600">Groups</label>
        <DynamicFilter
          v-model="selectedGroup"
          :multiselect="true"
          label="Groups"
          placeholder="Select Groups"
          :options="groupOptions"
          variant="outline"
          size="sm"
        />
      </div>

      <div class="space-y-1">
        <label class="block text-xs font-medium text-gray-600">Department</label>
        <DynamicFilter
          v-model="selectedDepartment"
          :multiselect="false"
          label="Department"
          placeholder="Select Department"
          :options="departmentOptions"
          variant="outline"
          size="sm"
        />
      </div>

      <div class="space-y-1">
        <label class="block text-xs font-medium text-gray-600">Class</label>
        <DynamicFilter
          v-model="selectedClass"
          :multiselect="true"
          label="Class"
          placeholder="Select Class"
          :options="classOptions"
          variant="outline"
          size="sm"
        />
      </div>

      <div class="space-y-1">
        <label class="block text-xs font-medium text-gray-600">Sub Class</label>
        <DynamicFilter
          v-model="selectedSubClass"
          :multiselect="true"
          label="Sub Class"
          placeholder="Select Sub Class"
          :options="subClassOptions"
          variant="outline"
          size="sm"
        />
      </div>
    </div>
    <div class="flex gap-4 justify-end">
       <button class="flex items-center gap-2 px-3 py-2 text-xs bg-tertiary text-white rounded hover:bg-green-900 transition-colors">
  <!-- Filter Icon -->
  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-.293.707L14 13.414V19a1 1 0 01-.553.894l-4 2A1 1 0 018 21v-7.586L3.293 6.707A1 1 0 013 6V4z"/>
  </svg>
  Apply Filters
</button>

      <button
        class="px-4 py-2 text-xs font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100"
        @click="clearAllFilters"
      >
        Clear All
      </button>
    </div>
  </div>
</template>
