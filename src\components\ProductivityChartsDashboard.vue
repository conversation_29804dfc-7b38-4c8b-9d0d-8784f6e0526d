<script setup lang="ts">
import ProductivityApexChart from './ProductivityApexChart.vue';
import ProductivityFilterBar from './ProductivityFilterBar.vue';
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { baseFastapiURL } from '../main';
// Types
interface RawDataItem {
  CLUSTER: number;
  GRP_NM: string;
  DPT_NM: string;
  CLSS_NM: string;
  SUB_CLSS_NM: string;
  x_raw: string;
  y: string;
  y_pred: string;
  sat_lm: number;
  perf_at_sat: number;
}

interface TransformedDataItem extends Omit<RawDataItem, 'x_raw' | 'y' | 'y_pred'> {
  x_raw_parsed: number[];
  y_parsed: number[];
  y_pred_parsed: number[];
  chartData: {
    originalData: { x: number; y: number }[];
    predictedData: { x: number; y: number }[];
  };
}

interface FilterState {
  cluster: number | null;
  subclass: string | null;
}

// State management
const state = reactive({
  dataset: [] as TransformedDataItem[],
  loading: false,
  error: null as string | null,
  currentChart: null as TransformedDataItem | null
});

const filters = reactive<FilterState>({
  cluster: null,
  subclass: null
});

// API service

const fetchPerformanceData = async (): Promise<RawDataItem[]> => {
  const response = await fetch(`${baseFastapiURL}/fetch-productivity-data`);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return data;
}

const subclassData: Record<string, {
  title: string;
  predictedData: { x: number; y: number }[];
  originalData: { x: number; y: number }[];
  r2: number;
  ridgeR2: number;
  diff: number;
}> = {
  mugs: {
    title: 'Productivity Prediction for Subclass: Mugs and Tea Sets',
    predictedData: generateParabolaData(-0.1, 85, 175, 60, 110, 2),
    originalData: [
      { x: 65, y: 125 },
      { x: 75, y: 170 },
      { x: 80, y: 175 },
      { x: 90, y: 170 },
      { x: 100, y: 165 },
      { x: 110, y: 130 }
    ],
    r2: 46, ridgeR2: 46, diff: 0
  },
  hbso: {
    title: 'Productivity Prediction for Subclass: HBSO',
    predictedData: generateParabolaData(-0.7, 19, 295, 7, 40, 2),
    originalData: [
      { x: 10, y: 280 },
      { x: 15, y: 290 },
      { x: 19, y: 295 },
      { x: 25, y: 290 },
      { x: 32, y: 150 },
      { x: 40, y: 120 }
    ],
    r2: 47, ridgeR2: 48, diff: 1
  },
  mirrors: {
    title: 'Productivity Prediction for Subclass: Mirrors',
    predictedData: generateParabolaData(-2.5, 19.5, 700, 16, 30, 1),
    originalData: [
      { x: 16, y: 680 },
      { x: 18, y: 690 },
      { x: 22, y: 690 },
      { x: 25, y: 680 },
      { x: 28, y: 470 }
    ],
    r2: 22, ridgeR2: 22, diff: 0
  },
  protectors: {
    title: 'Productivity Prediction for Subclass: Protectors',
    predictedData: generateParabolaData(-2.2, 25, 690, 18, 32, 1),
    originalData: [
      { x: 18, y: 670 },
      { x: 22, y: 680 },
      { x: 26, y: 690 },
      { x: 28, y: 660 },
      { x: 30, y: 650 },
      { x: 32, y: 640 }
    ],
    r2: 37, ridgeR2: 39, diff: 2
  }
};

const visibleCharts = computed(() => {
  if (selectedSubclass.value === 'all') {
    return Object.keys(subclassData);
  }
  return [selectedSubclass.value];
});
</script>

<template>
  <div class="flex w-full bg-main-bg">
    <div class="flex-1 flex flex-col overflow-hidden">
      <div class=" bg-primary">
        <div class="pl-12 pb-2">
                    <div class="">
            <FilterBarTest/>
          </div>
        </div>
      </div>
      <main class="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8 " >
        <div class="">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8 pt-5">
            <div v-for="key in visibleCharts" :key="key">
              <ProductivityApexChart
                :predictedData="subclassData[key].predictedData"
                :originalData="subclassData[key].originalData"
                :title="subclassData[key].title"
                :r2="subclassData[key].r2"
                :ridgeR2="subclassData[key].ridgeR2"
                :diff="subclassData[key].diff"
                :showModeBar="visibleCharts.length === 1"
              />
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>