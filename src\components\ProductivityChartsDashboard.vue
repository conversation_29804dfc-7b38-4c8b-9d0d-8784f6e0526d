<script setup lang="ts">
import ProductivityApexChart from './ProductivityApexChart.vue';
import { ref, computed } from 'vue';
import FilterBar from './FilterBar.vue';
import FilterBarTest from './FilterBarTest.vue';

const subclassOptions = [
  { label: 'All', value: 'all' },
  { label: 'Mugs and Tea Sets', value: 'mugs' },
  { label: 'HBSO', value: 'hbso' },
  { label: 'Mirrors', value: 'mirrors' },
  { label: 'Protectors', value: 'protectors' }
];

const selectedSubclass = ref('all');

// Helper to generate a perfect parabola
function generateParabolaData(a: number, h: number, k: number, xStart: number, xEnd: number, step: number) {
  const data = [];
  for (let x = xStart; x <= xEnd; x += step) {
    data.push({ x, y: a * (x - h) ** 2 + k });
  }
  return data;
}

const subclassData: Record<string, {
  title: string;
  predictedData: { x: number; y: number }[];
  originalData: { x: number; y: number }[];
  r2: number;
  ridgeR2: number;
  diff: number;
}> = {
  mugs: {
    title: 'Productivity Prediction for Subclass: Mugs and Tea Sets',
    predictedData: generateParabolaData(-0.1, 85, 175, 60, 110, 2),
    originalData: [
      { x: 65, y: 125 },
      { x: 75, y: 170 },
      { x: 80, y: 175 },
      { x: 90, y: 170 },
      { x: 100, y: 165 },
      { x: 110, y: 130 }
    ],
    r2: 46, ridgeR2: 46, diff: 0
  },
  hbso: {
    title: 'Productivity Prediction for Subclass: HBSO',
    predictedData: generateParabolaData(-0.7, 19, 295, 7, 40, 2),
    originalData: [
      { x: 10, y: 280 },
      { x: 15, y: 290 },
      { x: 19, y: 295 },
      { x: 25, y: 290 },
      { x: 32, y: 150 },
      { x: 40, y: 120 }
    ],
  },
  mirrors: {
    title: 'Productivity Prediction for Subclass: Mirrors',
    predictedData: generateParabolaData(-2.5, 19.5, 700, 16, 30, 1),
    originalData: [
      { x: 16, y: 680 },
      { x: 18, y: 690 },
      { x: 22, y: 690 },
      { x: 25, y: 680 },
      { x: 28, y: 470 }
    ],
  },
  protectors: {
    title: 'Productivity Prediction for Subclass: Protectors',
    predictedData: generateParabolaData(-2.2, 25, 690, 18, 32, 1),
    originalData: [
      { x: 18, y: 670 },
      { x: 22, y: 680 },
      { x: 26, y: 690 },
      { x: 28, y: 660 },
      { x: 30, y: 650 },
      { x: 32, y: 640 }
    ],
  }
};

const visibleCharts = computed(() => {
  if (selectedSubclass.value === 'all') {
    return Object.keys(subclassData);
  }
  return [selectedSubclass.value];
});
</script>

<template>
  <div class="flex w-full bg-main-bg">
    <div class="flex-1 flex flex-col overflow-hidden">
      <div class=" bg-primary">
        <div class="pl-12 pb-2">
                    <div class="">
            <FilterBarTest/>
          </div>
        </div>
      </div>
      <main class="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8 " >
        <div class="">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8 pt-5">
            <div v-for="key in visibleCharts" :key="key">
              <ProductivityApexChart
                :predictedData="subclassData[key].predictedData"
                :originalData="subclassData[key].originalData"
                :title="subclassData[key].title"
                :showModeBar="visibleCharts.length === 1"
              />
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template> 