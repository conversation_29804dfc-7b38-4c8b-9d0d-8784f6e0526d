<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import DynamicFilter from './common/DynamicFilter.vue'

interface FilterState {
  cluster: number | null;
  subclass: string | null;
}

const props = defineProps<{
  clusterOptions: number[];
  subclassOptions: string[];
  loading?: boolean;
}>();

const emit = defineEmits<{
  filtersApplied: [filters: FilterState];
  filtersCleared: [];
}>();

// Filter state
const selectedCluster = ref<number | null>(null);
const selectedSubclass = ref<string | null>(null);

// Convert cluster options to the format expected by DynamicFilter
const clusterFilterOptions = computed(() => 
  props.clusterOptions.map(cluster => cluster.toString())
);

// Watch cluster changes to reset subclass when cluster changes
watch(selectedCluster, (newCluster, oldCluster) => {
  if (newCluster !== oldCluster && selectedSubclass.value) {
    // Check if current subclass is still valid for the new cluster
    const availableSubclasses = props.subclassOptions;
    if (!availableSubclasses.includes(selectedSubclass.value)) {
      selectedSubclass.value = null;
    }
  }
});

// Apply filters
const applyFilters = () => {
  const filters: FilterState = {
    cluster: selectedCluster.value,
    subclass: selectedSubclass.value
  };
  emit('filtersApplied', filters);
};

// Clear all filters
const clearAllFilters = () => {
  selectedCluster.value = null;
  selectedSubclass.value = null;
  emit('filtersCleared');
};

// Handle cluster selection (convert string back to number)
const handleClusterChange = (value: string | null) => {
  selectedCluster.value = value ? parseInt(value) : null;
};
</script>

<template>
  <div class="p-6 max-w-6xl mx-auto space-y-8">
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
      <!-- Cluster Filter -->
      <div class="space-y-1">
        <label class="block text-xs font-medium text-gray-600">Cluster</label>
        <DynamicFilter
          :model-value="selectedCluster?.toString() || null"
          @update:model-value="handleClusterChange"
          :multiselect="false"
          label="Cluster"
          placeholder="Select Cluster"
          :options="clusterFilterOptions"
          variant="outline"
          size="sm"
          :disabled="loading"
        />
      </div>

      <!-- Subclass Filter -->
      <div class="space-y-1">
        <label class="block text-xs font-medium text-gray-600">Sub Class</label>
        <DynamicFilter
          v-model="selectedSubclass"
          :multiselect="false"
          label="Sub Class"
          placeholder="Select Sub Class"
          :options="subclassOptions"
          variant="outline"
          size="sm"
          :disabled="loading"
        />
      </div>

      <!-- Spacer columns for alignment -->
      <div class="hidden md:block"></div>
      <div class="hidden lg:block"></div>
      <div class="hidden lg:block"></div>
      <div class="hidden lg:block"></div>
    </div>

    <!-- Action Buttons -->
    <div class="flex gap-4 justify-end">
      <button 
        class="flex items-center gap-2 px-3 py-2 text-xs bg-tertiary text-white rounded hover:bg-green-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        @click="applyFilters"
        :disabled="loading"
      >
        <!-- Filter Icon -->
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-.293.707L14 13.414V19a1 1 0 01-.553.894l-4 2A1 1 0 018 21v-7.586L3.293 6.707A1 1 0 013 6V4z"/>
        </svg>
        {{ loading ? 'Loading...' : 'Apply Filters' }}
      </button>

      <button
        class="px-4 py-2 text-xs font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 disabled:opacity-50 disabled:cursor-not-allowed"
        @click="clearAllFilters"
        :disabled="loading"
      >
        Clear All
      </button>
    </div>
  </div>
</template>
