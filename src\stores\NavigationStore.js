import { defineStore } from 'pinia'
import BusinessDashboard from '../components/BusinessDashboard.vue'
import StoreCluster from '../components/StoreCluster.vue'
import ControlStoreSelection from '../components/ControlStoreSelection.vue'
import SpaceDataSummary from '../components/SpaceDataSummary.vue'
import SpaceHealthDashboard from '../components/SpaceHealthDashboard.vue'
import EvaluationDashboard from '../components/EvaluationDashboard.vue'
import OptimizationSummary from '../components/Optimization.vue'
import RangeBasedOptimisationSummary from '../components/OptimizationSummary.vue'
import SaturationPoint from '../components/ProductivityChartsDashboard.vue'
import OutlierHandle from '../components/OutlierHandle.vue'
import PerformanceCalculation from '../components/PerformanceCalculation.vue'


export const useStepsStore = defineStore('steps', {
  state: () => ({
    steps: [
      { name: 'Business Requirement', component: BusinessDashboard, disabled: false, hidden: false },
      { name: 'Store Clustering', component: StoreCluster, disabled: true, hidden: true },
      { name: 'Control & Test Store', component: ControlStoreSelection, disabled: true, hidden: true },
      { name: 'Space Data Summary', component: SpaceDataSummary, disabled: true, hidden: false },
      { name: 'Outlier Handle', component: OutlierHandle, disabled: true, hidden: false },
      { name: 'Space Health Dashboard', component: SpaceHealthDashboard, disabled: true, hidden: false },
      { name: 'Performance Calculation', component: PerformanceCalculation, disabled: true, hidden: false },
      { name: 'Range Based Optimisation Summary', component: RangeBasedOptimisationSummary, disabled: true, hidden: true },
      { name: 'Optimization Summary', component: OptimizationSummary, disabled: true, hidden: true },
      { name: 'Productivity Charts', component: SaturationPoint, disabled: true, hidden: false },
      { name: 'Evaluation', component: EvaluationDashboard, disabled: true, hidden: false }
    ],
    currentStep: 0
  }),

  getters: {
    visibleSteps: (state) => state.steps.filter(step => !step.hidden),
    activeStep: (state) => state.visibleSteps[state.currentStep]
  },

  actions: {
    goToStep(index) {
      if (!this.visibleSteps[index].disabled) {
        this.currentStep = index
      }
    },
    goToNextStep() {
      if (this.currentStep < this.visibleSteps.length - 1) {
        this.visibleSteps[this.currentStep + 1].disabled = false
        this.currentStep++
      }
    },
    setStepVisible(stepName, hidden = true) {
      const step = this.steps.find(s => s.name === stepName)
      if (step) step.hidden = hidden
    }

  }
})